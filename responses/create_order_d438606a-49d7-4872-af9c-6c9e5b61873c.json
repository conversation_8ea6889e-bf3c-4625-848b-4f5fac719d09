{"timestamp": "2025-05-28T17:22:45.478+00:00", "status": 400, "error": "Bad Request", "message": "JSON parse error: Illegal unquoted character ((CTRL-<PERSON><PERSON>, code 27)): has to be escaped using backslash to be included in string value; nested exception is com.fasterxml.jackson.databind.JsonMappingException: Illegal unquoted character ((CTRL-<PERSON><PERSON>, code 27)): has to be escaped using backslash to be included in string value\n at [Source: (PushbackInputStream); line: 2, column: 22] (through reference chain: me.keepz.keepzwalletecommerceservice.model.param.EncryptedParam[\"encryptedData\"])", "path": "/api/integrator/order"}