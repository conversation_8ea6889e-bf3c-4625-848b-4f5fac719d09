import { Checkout } from "types/commonTypes";

export function useCheckout() {
  const { $warningToast } = useNuxtApp();
  const { orders } = useOrder();
  const router = useRouter();
  const { t } = useI18n();
  const config = useRuntimeConfig();
  const { createKeepzOrder } = useKeepz();

  const pending = ref(false);
  const { encodedID } = useId();
  const user = useAuthUser();
  const keepzRedirectUrl = ref("");

  const indicator = ref("");

  const checkout = async (
    method: string,
    tips = 0,
    discount_code = null,
    change = null
  ) => {
    indicator.value = method;
    pending.value = true;
    
    // Handle Keepz payment separately
    if (method === "keepz") {
      const orderId = `order-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
      const { data, error } = await apiPost<{
        data: Partial<Checkout>;
      }>("/checkout", {
        table_id: encodedID.value,
        payment_method: method,
        tips,
        discount_code,
        change,
        integrator_order_id: orderId
      });
      
      if (error.value) {
        $warningToast(t("NoOrdPayMethod"));
        pending.value = false;
        return { data, error, client_ids: null };
      }
      
      // Get total amount from checkout data
      const amount = data.value?.data?.amount || 0;
      
      // Create Keepz order
      const keepzResponse = await createKeepzOrder(amount, orderId);
      
      if (keepzResponse.success && keepzResponse.urlForQR) {
        keepzRedirectUrl.value = keepzResponse.urlForQR;
        
        // Get and map the client_ids to comma separated string
        const client_ids = orders.value
          ?.filter(order => order.client_id !== undefined && order.client_id !== null)
          .map(order => order.client_id)
          .join(",");
          
        orders.value = [];
        pending.value = false;
        return { data, error: ref(null), client_ids };
      } else {
        $warningToast(t("keepzPaymentFailed"));
        pending.value = false;
        return { data: null, error: ref(true), client_ids: null };
      }
    }
    
    // Original checkout logic for other payment methods
    const { data, error } = await apiPost<{
      data: Partial<Checkout>;
    }>("/checkout", {
      table_id: encodedID.value,
      payment_method: method,
      tips,
      discount_code,
      change,
    });
    const client_ids = orders.value
      ?.filter(
        (order) => order.client_id !== undefined && order.client_id !== null
      )
      .map((order) => order.client_id)
      .join(",");

    if (error.value) {
      $warningToast(t("NoOrdPayMethod"));
    } else {
      // Get and map the client_ids to comma separated string
      orders.value = [];
    }

    pending.value = false;

    return { data, error, client_ids };
  };

  const getCheck = async (checkId: string) => {
    pending.value = true;
    const { data: check, error } = await apiGet(`/checkout/${checkId}`);
    if (error.value) {
      $warningToast(t("NoOrdPayMethod"));
    }

    return { check, error };
  };

  const redirectToKeepzPayment = () => {
    if (keepzRedirectUrl.value) {
      window.location.href = keepzRedirectUrl.value;
    }
  };

  return { 
    getCheck, 
    checkout, 
    pending, 
    indicator, 
    keepzRedirectUrl,
    redirectToKeepzPayment 
  };
}
