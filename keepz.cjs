#!/usr/bin/env node

const crypto = require('crypto');
const https = require('https');
const { URL } = require('url');

// Environment variables (equivalent to export statements)
const KEEPZ_INTEGRATOR_ID = "05382470-0ddb-4e36-8f23-4939f3b81ec9";
const KEEPZ_IDENTIFIER = "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAytWVQEPpjRiWDpDJtmYWEPHm+QPFhDSs2eWvkdNhUesE43bZ/b0SGnopAyONZOlLDmM89qLXIJtRUVxnkGiVxtvQ0bsPBwXM7eN3dVxJYVPDSyVeP4sP8hCG9HnjUdzDNae6ro12uaEAFFOCF8/xCd7DPKjTmAA6BA+FIi4cb7qo4cp2UXlQ+1j25Vu7l2ZIFwC7dv/XYgM6fgebTrlTR+9MdwZbtU30Tdi3Z6sbZ2L4OMt226w/n4nrNfCw/E03Iyp28y3ElTTBKGDLEZrqGodR+djFF+KHDMtRLjlqXZ0unDIOjFOZzALzidT++YmNYCHUTjySNtoW7r7KAZBPMgNUBL6K7HPsb2XX1a4nHMXrRi5JkoNfc47FZCp7juoOFe2HdQCnDwyD2Xc9svtJx7NfuoCBtbE9v0FhY7kFyTlpCeD6Mcnoge91n7IzxhY/IOQ4oX009RXWpWsXXfHA0gxQgmRYUJdQnG/3+jS4Ugi2GoA0k6IKEyjQA/yKPuiH7Hve5habYEhOtgvnwiSqvxwf1KzYvhSB+0MBNlUwMJVaWKbN5Tm+DlDTzAWlSLdw46WQWUu+POaToFSEWRG0XlEkKrVOqRM7o4yAJnB7fhG2RE24h4/467i2WeL6XCocyOAai3GtkB+TJGePNkx1YczDdoqejt614Qu0s0SubQcCAwEAAQ==";
const KEEPZ_RECEIVER_ID = "0349ce8d-8061-4867-94a9-b9de0fb3fec6";
const ORDER_AMOUNT = "25.50";
const KEEPZ_BASE_URL = process.env.KEEPZ_BASE_URL || "https://gateway.dev.keepz.me/ecommerce-service";

// Generate UUID (equivalent to uuidgen)
function generateUUID() {
    return crypto.randomUUID();
}

// Create the data object and encode it
const orderData = {
    amount: ORDER_AMOUNT,
    receiverId: KEEPZ_RECEIVER_ID,
    receiverType: "BRANCH",
    integratorId: KEEPZ_INTEGRATOR_ID,
    integratorOrderId: generateUUID()
};

// Convert to JSON and base64 encode (equivalent to the shell pipeline)
const jsonData = JSON.stringify(orderData);
const edata = Buffer.from(jsonData).toString('base64');

// Debug output (equivalent to echo $edata | base64 -d)
console.log(Buffer.from(edata, 'base64').toString());
console.log();

// Prepare the API request payload
const requestPayload = {
    encryptedData: edata,
    identifier: KEEPZ_IDENTIFIER
};

// Parse the URL
const apiUrl = new URL(`${KEEPZ_BASE_URL}/api/integrator/order`);

// Configure the request options
const requestOptions = {
    hostname: apiUrl.hostname,
    port: apiUrl.port || (apiUrl.protocol === 'https:' ? 443 : 80),
    path: apiUrl.pathname,
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(JSON.stringify(requestPayload))
    }
};

// Make the HTTP request (equivalent to curl)
const req = https.request(requestOptions, (res) => {
    let responseData = '';

    res.on('data', (chunk) => {
        responseData += chunk;
    });

    res.on('end', () => {
        console.log('Response Status:', res.statusCode);
        console.log('Response Headers:', res.headers);
        console.log('Response Body:', responseData);
    });
});

req.on('error', (error) => {
    console.error('Request failed:', error.message);
    process.exit(1);
});

// Send the request
req.write(JSON.stringify(requestPayload));
req.end();