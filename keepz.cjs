#!/usr/bin/env node

const crypto = require('crypto');
const https = require('https');
const { URL } = require('url');

// Environment variables
const KEEPZ_INTEGRATOR_ID = process.env.KEEPZ_INTEGRATOR_ID || "05382470-0ddb-4e36-8f23-4939f3b81ec9";
const KEEPZ_IDENTIFIER = process.env.KEEPZ_IDENTIFIER || "05382470-0ddb-4e36-8f23-4939f3b81ec9";
const KEEPZ_RECEIVER_ID = process.env.KEEPZ_RECEIVER_ID || "0349ce8d-8061-4867-94a9-b9de0fb3fec6";
const ORDER_AMOUNT = process.env.ORDER_AMOUNT || "25.50";
const KEEPZ_BASE_URL = process.env.KEEPZ_BASE_URL || "https://gateway.dev.keepz.me/ecommerce-service";

// RSA Keys from environment variables
const KEEPZ_PUBLIC_KEY = process.env.KEEPZ_PUBLIC_KEY;
const KEEPZ_PRIVATE_KEY = process.env.KEEPZ_PRIVATE_KEY;

// Validate required environment variables
if (!KEEPZ_PUBLIC_KEY) {
    console.error('Error: KEEPZ_PUBLIC_KEY environment variable is required');
    process.exit(1);
}

if (!KEEPZ_PRIVATE_KEY) {
    console.error('Error: KEEPZ_PRIVATE_KEY environment variable is required');
    process.exit(1);
}

// Generate UUID (equivalent to uuidgen)
function generateUUID() {
    return crypto.randomUUID();
}

// Convert base64 key to PEM format
function convertToPemFormat(base64Key, keyType = 'PUBLIC') {
    const pemHeader = keyType === 'PUBLIC' ? '-----BEGIN PUBLIC KEY-----' : '-----BEGIN PRIVATE KEY-----';
    const pemFooter = keyType === 'PUBLIC' ? '-----END PUBLIC KEY-----' : '-----END PRIVATE KEY-----';
    const pemBody = base64Key.match(/.{1,64}/g).join('\n');
    return `${pemHeader}\n${pemBody}\n${pemFooter}`;
}

// Encrypt data using RSA public key
function encryptData(data, publicKey) {
    try {
        const buffer = Buffer.from(data, 'utf8');
        const encrypted = crypto.publicEncrypt({
            key: publicKey,
            padding: crypto.constants.RSA_PKCS1_PADDING
        }, buffer);
        return encrypted.toString('base64');
    } catch (error) {
        console.error('Encryption failed:', error.message);
        throw error;
    }
}

// Decrypt data using RSA private key
function decryptData(encryptedData, privateKey) {
    try {
        const buffer = Buffer.from(encryptedData, 'base64');
        const decrypted = crypto.privateDecrypt({
            key: privateKey,
            padding: crypto.constants.RSA_PKCS1_PADDING
        }, buffer);
        return decrypted.toString('utf8');
    } catch (error) {
        console.error('Decryption failed:', error.message);
        throw error;
    }
}

// Create the order data object
const orderData = {
    amount: parseFloat(ORDER_AMOUNT), // Convert to number as per API docs
    receiverId: KEEPZ_RECEIVER_ID,
    receiverType: "BRANCH",
    integratorId: KEEPZ_INTEGRATOR_ID,
    integratorOrderId: generateUUID()
};

console.log('Order data to be encrypted:', JSON.stringify(orderData, null, 2));

// Convert JSON to string and encrypt it
const jsonData = JSON.stringify(orderData);
const publicKeyPem = convertToPemFormat(KEEPZ_PUBLIC_KEY, 'PUBLIC');

console.log('Using public key for encryption');
console.log();

let encryptedData;
try {
    encryptedData = encryptData(jsonData, publicKeyPem);
    console.log('Successfully encrypted data');
    console.log('Encrypted data length:', encryptedData.length);
} catch (error) {
    console.error('Failed to encrypt data:', error.message);
    process.exit(1);
}

// Prepare the API request payload
const requestPayload = {
    encryptedData: encryptedData,
    identifier: KEEPZ_IDENTIFIER
};

console.log('Request payload prepared');
console.log('Identifier:', KEEPZ_IDENTIFIER);
console.log();

// Parse the URL
const apiUrl = new URL(`${KEEPZ_BASE_URL}/api/integrator/order`);
console.log('Making API call to:', apiUrl.toString());

// Configure the request options
const requestOptions = {
    hostname: apiUrl.hostname,
    port: apiUrl.port || (apiUrl.protocol === 'https:' ? 443 : 80),
    path: apiUrl.pathname,
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(JSON.stringify(requestPayload))
    }
};

// Make the HTTP request
const req = https.request(requestOptions, (res) => {
    let responseData = '';

    res.on('data', (chunk) => {
        responseData += chunk;
    });

    res.on('end', () => {
        console.log('Response Status:', res.statusCode);
        console.log('Response Headers:', JSON.stringify(res.headers, null, 2));
        console.log('Response Body:', responseData);

        if (res.statusCode === 200 || res.statusCode === 201) {
            try {
                const response = JSON.parse(responseData);
                console.log('\nAPI call successful!');

                if (response.encryptedData) {
                    console.log('Encrypted response data received');

                    // Decrypt the response using private key
                    const privateKeyPem = convertToPemFormat(KEEPZ_PRIVATE_KEY, 'PRIVATE');

                    try {
                        const decryptedResponse = decryptData(response.encryptedData, privateKeyPem);
                        console.log('Successfully decrypted response');
                        console.log('Decrypted response:', JSON.stringify(JSON.parse(decryptedResponse), null, 2));

                        const responseObj = JSON.parse(decryptedResponse);
                        if (responseObj.systemId) {
                            console.log('\nOrder created successfully!');
                            console.log('System ID:', responseObj.systemId);
                            console.log('Integrator Order ID:', responseObj.integratorOrderId);
                            if (responseObj.urlForQR) {
                                console.log('QR URL:', responseObj.urlForQR);
                            }
                        }
                    } catch (decryptError) {
                        console.error('Failed to decrypt response:', decryptError.message);
                    }
                } else {
                    console.log('No encrypted data in response');
                }
            } catch (parseError) {
                console.log('Response is not valid JSON:', parseError.message);
            }
        } else {
            console.error('API call failed with status:', res.statusCode);
        }
    });
});

req.on('error', (error) => {
    console.error('Request failed:', error.message);
    process.exit(1);
});

// Send the request
req.write(JSON.stringify(requestPayload));
req.end();