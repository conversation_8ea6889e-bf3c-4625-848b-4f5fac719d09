// https://nuxt.com/docs/api/configuration/nuxt-config
import { process } from "unenv/runtime/node/process/_process";

export default defineNuxtConfig({
  srcDir: "src/",
  ssr: false, // Ensure SPA mode
  vite: {
    server: {
      hmr: {
        clientPort: 24600,
        port: 24600,
      },
    },
  },
  app: {
    head: {
      title: 'MenuHub',
      link: [
        // { rel: 'stylesheet', href: "/assets/css/bootstrap.min.css" },
        // { rel: 'stylesheet', href: "/assets/font-awesome/4.2.0/css/font-awesome.min.css" },
        // { rel: 'stylesheet', href: "/assets/fonts/fonts.googleapis.com.css" },
        // { rel: 'stylesheet', href: "/assets/css/ace.min.css" },
        // { rel: 'stylesheet', href: "/assets/css/ekko-lightbox.min.css" },
        // { rel: "stylesheet", href: "/css/osahan.css" },
        { rel: "stylesheet", href: "/css/vendor/icofont/icofont.min.css" },
        { rel: "stylesheet", href: "/css/vendor/fontawesome/all.min.css" },
        // { rel: "stylesheet", href: "/css/vendor/bootstrap/bootstrap.min.css" },
        { rel: "stylesheet", href: "/css/input.css" },
      ],
      script: [
        // { src: '/assets/js/html5shiv.min.js' },
        // { src: '/assets/js/respond.min.js' },
        // { src: '/assets/js/jquery.2.1.1.min.js' },
        // { src: '/assets/js/bootstraps.js' },
        // { src: '/assets/js/jqueries.js' },
        // { src: '/assets/js/select2.min.js' },
        // { src: '/assets/js/fuelux.spinner.min.js' },
        // { src: '/assets/js/ace-elements.min.js' },
        // { src: '/assets/js/ace-editable.min.js' },
        // { src: '/assets/js/ace.min.js' },
        // { src: '/assets/js/ekko-lightbox.min.js' },
        {
          src: `https://maps.googleapis.com/maps/api/js?key=${process.env.GOOGLE_MAPS_API_KEY}`,
          async: true,
          defer: true
        }
      ],
      style: [],
    },
  },
  imports: {
    dirs: ["src/composables"],
  },
  dir: {
    public: "../public/",
  },
  css: [
    // "/assets/css/bootstrap.min.css",
    // "/assets/font-awesome/4.2.0/css/font-awesome.min.css",
    // "/assets/fonts/fonts.googleapis.com.css",
    // "/assets/css/ace.min.css"
    // '/css/osahan',
    // '/css/vendor/icofont/icofont.min',
    // '/css/vendor/fontawesome/all.min',
    // '/css/vendor/bootstrap/bootstrap.min',
  ],
  modules: [
    "@nuxtjs/supabase",
    "nuxt-swiper",
    "@nuxtjs/i18n",
    "@nuxtjs/google-fonts",
    "@nuxtjs/tailwindcss",
    "nuxt-icon",
    "@nuxt/image",
    "@/modules/nuxt-spa-loading-template/module",
  ],
  i18n: {
    langDir: 'locales',
    locales: [
      { code: 'en', file: 'en.ts' },
      { code: 'ka', file: 'ka.ts' },
      { code: 'ru', file: 'ru.ts' },
      { code: 'el', file: 'el.ts' }
    ],
    defaultLocale: 'en',
    strategy: 'no_prefix',
    detectBrowserLanguage: false,
    vueI18n: {
      legacy: false,
      locale: 'en',
      fallbackLocale: 'en',
      fallbackWarn: false,
      missingWarn: false
    }
  },
  supabase: {
    cookieName: "sb-mhb",
    url: process.env.NUXT_PUBLIC_SUPABASE_URL,
    key: process.env.NUXT_PUBLIC_SUPABASE_KEY,
    redirect: true,
    redirectOptions: {
      login: "/login",
      callback: "/confirm",
      exclude: ["*"],
    },
  },
  spaLoadingTemplate: "custom-loader.html",
  googleFonts: {
    families: {
      Allison: true,
      Quicksand: [100, 200, 300, 400, 500, 600, 700],
      NatoSansGeorgian: [100, 200, 300, 400, 500, 600, 700],
    },
  },

  plugins: ["src/plugins/pusher.ts"],
  runtimeConfig: {
    apiSecret: "123",
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE,
      capiBase: process.env.NUXT_PUBLIC_CAPI_BASE,
      audioBase: process.env.NUXT_PUBLIC_AUDIO_BASE,
      domainBase: process.env.NUXT_PUBLIC_DOMAIN_BASE,
      domainEmail: process.env.NUXT_PUBLIC_DOMAIN_EMAIL,
      domainPhone: process.env.NUXT_PUBLIC_PHONE,
      domainAddress: process.env.NUXT_PUBLIC_ADDRESS,
      pusherHost: process.env.NUXT_PUBLIC_PUSHER_HOST,
      pusherCluster: process.env.NUXT_PUBLIC_PUSHER_CLUSTER,
      pusherKey: process.env.NUXT_PUBLIC_PUSHER_KEY,
      defaultImage:
        "https://r2.mnu.ge/RPXFbylQysqq4s7gZggn9iVTpRbIrcxloc6fiKOa.png",
      supabaseUrl: process.env.NUXT_PUBLIC_SUPABASE_URL,
      supabaseKey: process.env.NUXT_PUBLIC_SUPABASE_KEY,
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL,
      adminToken: process.env.NUXT_PUBLIC_ADMIN_TOKEN,
      cookieDomain: process.env.NUXT_PUBLIC_COOKIE_DOMAIN,
      r2Url: process.env.NUXT_PUBLIC_R2_URL,
      keepzBaseUrl: process.env.KEEPZ_BASE_URL || 'https://gateway.dev.keepz.me/ecommerce-service',
      keepzIntegratorId: process.env.KEEPZ_INTEGRATOR_ID,
      keepzReceiverId: process.env.KEEPZ_RECEIVER_ID,
      keepzIdentifier: process.env.KEEPZ_IDENTIFIER,
    },
  },
  build: {
    transpile: ["vue-toastification"],
  },
  components: ["~/components"],
  router: {
    options: {
      strict: false
    }
  },
  routeRules: {
    '/**': { ssr: false }
  },
});
